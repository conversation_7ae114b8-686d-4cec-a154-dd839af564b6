#!/usr/bin/env python3
"""
条件生成信息查看工具

这个脚本帮助您了解：
1. 类别ID到物体名称的映射关系
2. 如何设置区域条件生成
3. 数据集中可用的条件信息

使用方法:
    python tools/check_conditioning_info.py --exp_path /path/to/experiment --data_root data/ACRONYM
"""

import argparse
import os
import sys
import numpy as np

# 添加路径以便导入模块
sys.path.append(os.getcwd())

from grasp_ldm.dataset.acronym import FILTER_63_CATEGORIES
from tools.inference import InferenceLDM, InferenceVAE


def print_class_mapping():
    """打印类别ID到物体名称的映射"""
    print("=" * 60)
    print("ACRONYM数据集类别ID映射表")
    print("=" * 60)
    print("类别ID | 物体名称")
    print("-" * 30)
    
    for idx, category in enumerate(FILTER_63_CATEGORIES):
        print(f"{idx:6d} | {category}")
    
    print(f"\n总共 {len(FILTER_63_CATEGORIES)} 个类别")
    print("=" * 60)


def analyze_dataset_sample(model, sample_idx=0):
    """分析数据集样本以了解区域条件信息"""
    print("\n" + "=" * 60)
    print("数据集样本分析")
    print("=" * 60)
    
    if len(model.dataset) == 0:
        print("错误：数据集为空")
        return
    
    # 获取一个样本
    sample_idx = min(sample_idx, len(model.dataset) - 1)
    dataitem = model.dataset.__getitem__(index=sample_idx)
    
    print(f"样本索引: {sample_idx}")
    print(f"数据集大小: {len(model.dataset)}")
    
    # 分析metas信息
    metas = dataitem.get('metas', {})
    print(f"\n物体信息:")
    print(f"  - 类别: {metas.get('category', 'Unknown')}")
    print(f"  - 缩放: {metas.get('scale', 'Unknown')}")
    print(f"  - 网格路径: {metas.get('mesh_path', 'Unknown')}")
    
    # 分析点云信息
    pc = dataitem.get('pc')
    if pc is not None:
        print(f"\n点云信息:")
        print(f"  - 形状: {pc.shape}")
        print(f"  - 数据类型: {pc.dtype}")
        print(f"  - 范围: [{pc.min():.3f}, {pc.max():.3f}]")
    
    # 检查是否有区域信息
    region_points = metas.get('region_points')
    if region_points is not None:
        print(f"\n区域信息:")
        print(f"  - 区域点形状: {region_points.shape}")
        print(f"  - 可用区域数量: {region_points.shape[0] if len(region_points.shape) > 1 else 1}")
        print(f"  - 建议region_id范围: 0 到 {region_points.shape[0]-1 if len(region_points.shape) > 1 else 0}")
    else:
        print(f"\n区域信息: 此样本不包含区域信息")
        print(f"  - 区域条件生成可能不可用于此数据集类型")
    
    # 获取类别在列表中的索引
    category = metas.get('category', '')
    if category in FILTER_63_CATEGORIES:
        class_id = FILTER_63_CATEGORIES.index(category)
        print(f"\n类别条件信息:")
        print(f"  - 当前物体类别: {category}")
        print(f"  - 对应的class_id: {class_id}")
        print(f"  - 使用示例: --conditioning class --condition_value {class_id}")


def print_usage_examples():
    """打印使用示例"""
    print("\n" + "=" * 60)
    print("使用示例")
    print("=" * 60)
    
    print("\n1. 无条件生成 (VAE和LDM都支持):")
    print("python generate_grasps.py \\")
    print("    --exp_path /path/to/experiment \\")
    print("    --mode LDM \\")
    print("    --conditioning unconditional \\")
    print("    --num_grasps 20")
    
    print("\n2. 类别条件生成 (仅LDM支持):")
    print("# 生成针对杯子(Cup)的抓取")
    print("python generate_grasps.py \\")
    print("    --exp_path /path/to/experiment \\")
    print("    --mode LDM \\")
    print("    --conditioning class \\")
    print("    --condition_value 0 \\")
    print("    --num_grasps 20")
    
    print("\n# 生成针对碗(Bowl)的抓取")
    print("python generate_grasps.py \\")
    print("    --exp_path /path/to/experiment \\")
    print("    --mode LDM \\")
    print("    --conditioning class \\")
    print("    --condition_value 5 \\")
    print("    --num_grasps 20")
    
    print("\n3. 区域条件生成 (仅LDM支持，需要数据集支持):")
    print("python generate_grasps.py \\")
    print("    --exp_path /path/to/experiment \\")
    print("    --mode LDM \\")
    print("    --conditioning region \\")
    print("    --condition_value 0 \\")
    print("    --num_grasps 20")


def main():
    parser = argparse.ArgumentParser(description="条件生成信息查看工具")
    parser.add_argument("--exp_path", type=str, help="实验路径")
    parser.add_argument("--data_root", type=str, default="data/ACRONYM", help="数据根目录")
    parser.add_argument("--mode", type=str, choices=["VAE", "LDM"], default="LDM", help="模型类型")
    parser.add_argument("--sample_idx", type=int, default=0, help="要分析的样本索引")
    
    args = parser.parse_args()
    
    # 总是显示类别映射
    print_class_mapping()
    
    # 如果提供了实验路径，分析数据集
    if args.exp_path:
        try:
            print(f"\n正在加载模型: {args.mode}")
            exp_name = os.path.basename(args.exp_path)
            exp_out_root = os.path.dirname(args.exp_path)
            
            if args.mode == "LDM":
                model = InferenceLDM(
                    exp_name=exp_name,
                    exp_out_root=exp_out_root,
                    data_root=args.data_root,
                    load_dataset=True,
                    data_split="test",
                    use_ema_model=True,
                )
            else:
                model = InferenceVAE(
                    exp_name=exp_name,
                    exp_out_root=exp_out_root,
                    data_root=args.data_root,
                    load_dataset=True,
                    data_split="test",
                    use_ema_model=True,
                )
            
            analyze_dataset_sample(model, args.sample_idx)
            
        except Exception as e:
            print(f"\n错误：无法加载模型或数据集")
            print(f"错误信息: {e}")
            print(f"请检查实验路径和数据根目录是否正确")
    
    # 显示使用示例
    print_usage_examples()


if __name__ == "__main__":
    main()
